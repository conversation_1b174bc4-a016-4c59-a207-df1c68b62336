#!/bin/bash

# Simple script to run the IPN migration
# This assumes you have psql installed and configured

echo "=== Running IPN Migration ==="
echo "This migration confirms the database schema is ready for IPN webhooks"
echo ""

# Check if migration file exists
if [ ! -f "migrations/add_transaction_fields_for_ipn.sql" ]; then
    echo "Error: Migration file not found!"
    echo "Make sure you're running this from the project root directory"
    exit 1
fi

# Display the migration content
echo "Migration content:"
echo "=================="
cat migrations/add_transaction_fields_for_ipn.sql
echo ""
echo "=================="
echo ""

# Ask for confirmation
read -p "Do you want to run this migration? (y/N): " -n 1 -r
echo ""

if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "Running migration..."
    
    # You'll need to replace these with your actual database connection details
    # Or use environment variables
    DB_HOST="${DB_HOST:-localhost}"
    DB_PORT="${DB_PORT:-5432}"
    DB_NAME="${DB_NAME:-phantom}"
    DB_USER="${DB_USER:-postgres}"
    
    echo "Connecting to database: $DB_HOST:$DB_PORT/$DB_NAME as $DB_USER"
    
    # Run the migration
    psql -h "$DB_HOST" -p "$DB_PORT" -d "$DB_NAME" -U "$DB_USER" -f migrations/add_transaction_fields_for_ipn.sql
    
    if [ $? -eq 0 ]; then
        echo "✅ Migration completed successfully!"
    else
        echo "❌ Migration failed!"
        exit 1
    fi
else
    echo "Migration cancelled."
    exit 0
fi

echo ""
echo "=== Migration Complete ==="
echo "Database schema is confirmed ready for IPN webhooks"
echo "You can now test the IPN webhook with:"
echo "  node check_ipn.js  # Quick health check"
echo "  node test_ipn.js   # Full test suite"
