#!/usr/bin/env node

// Simple migration runner for the IPN transaction fields
// This connects to Supabase and runs the migration SQL

import { createClient } from '@supabase/supabase-js';
import { readFileSync } from 'fs';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables
const SUPABASE_URL = process.env.PUBLIC_SUPABASE_URL || process.env.SUPABASE_URL;
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_ANON_KEY;

if (!SUPABASE_URL || !SUPABASE_SERVICE_KEY) {
  console.error('Missing Supabase configuration. Please set:');
  console.error('- PUBLIC_SUPABASE_URL or SUPABASE_URL');
  console.error('- SUPABASE_SERVICE_ROLE_KEY or SUPABASE_ANON_KEY');
  process.exit(1);
}

const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

async function runMigration() {
  try {
    console.log('Running IPN transaction fields migration...');
    
    // Read the migration SQL file
    const migrationPath = join(__dirname, 'migrations', 'add_transaction_fields_for_ipn.sql');
    const migrationSQL = readFileSync(migrationPath, 'utf8');
    
    console.log('Migration SQL:');
    console.log(migrationSQL);
    console.log('\n' + '='.repeat(50) + '\n');
    
    // Execute the migration
    const { data, error } = await supabase.rpc('exec_sql', { sql: migrationSQL });
    
    if (error) {
      console.error('Migration failed:', error);
      process.exit(1);
    }
    
    console.log('Migration completed successfully!');
    console.log('Result:', data);
    
  } catch (err) {
    console.error('Error running migration:', err);
    process.exit(1);
  }
}

async function testConnection() {
  try {
    console.log('Testing Supabase connection...');
    
    // Test connection by querying a simple table
    const { data, error } = await supabase
      .from('teams')
      .select('id')
      .limit(1);
    
    if (error) {
      console.error('Connection test failed:', error);
      return false;
    }
    
    console.log('Connection successful!');
    return true;
    
  } catch (err) {
    console.error('Connection error:', err);
    return false;
  }
}

async function main() {
  console.log('=== Database Migration Runner ===');
  console.log('Supabase URL:', SUPABASE_URL);
  console.log('Service Key:', SUPABASE_SERVICE_KEY ? '[CONFIGURED]' : '[MISSING]');
  console.log('');
  
  // Test connection first
  const connected = await testConnection();
  if (!connected) {
    console.error('Cannot connect to database. Exiting.');
    process.exit(1);
  }
  
  // Run migration
  await runMigration();
  
  console.log('\n=== Migration Complete ===');
}

if (process.argv[1] === fileURLToPath(import.meta.url)) {
  main().catch(console.error);
}
