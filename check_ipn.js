#!/usr/bin/env node

// Quick check script for IPN webhook endpoint
const axios = require('axios');

const BASE_URL = process.env.BASE_URL || 'http://localhost:5173';
const IPN_URL = `${BASE_URL}/api/notifications/ipn`;

async function checkEndpoint() {
  console.log('🔍 Checking IPN endpoint...');
  console.log('URL:', IPN_URL);
  
  try {
    const response = await axios.get(IPN_URL, { timeout: 5000 });
    console.log('✅ Endpoint is accessible');
    console.log('Response:', response.data);
    return true;
  } catch (error) {
    if (error.code === 'ECONNREFUSED') {
      console.log('❌ Server is not running');
      console.log('💡 Start the server with: npm run dev');
    } else if (error.response) {
      console.log('⚠️  Endpoint responded with error:', error.response.status);
      console.log('Response:', error.response.data);
    } else {
      console.log('❌ Network error:', error.message);
    }
    return false;
  }
}

async function checkCurrencyService() {
  console.log('\n💱 Testing currency conversion...');
  
  // This would require importing the currency service
  // For now, just show what rates should be available
  const expectedRates = {
    'USDTTRC': 1.0,
    'BTC': '~$45,000',
    'ETH': '~$2,500',
    'TRX': '~$0.08'
  };
  
  console.log('Expected conversion rates:');
  Object.entries(expectedRates).forEach(([currency, rate]) => {
    console.log(`  ${currency}: ${rate}`);
  });
}

async function main() {
  console.log('=== IPN Webhook Health Check ===\n');
  
  const endpointOk = await checkEndpoint();
  await checkCurrencyService();
  
  console.log('\n=== Summary ===');
  if (endpointOk) {
    console.log('✅ IPN webhook is ready for testing');
    console.log('🧪 Run full tests with: node test_ipn.js');
  } else {
    console.log('❌ IPN webhook is not accessible');
    console.log('🔧 Check server status and try again');
  }
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { checkEndpoint, checkCurrencyService };
