#!/usr/bin/env node

// Test script for IPN webhook endpoint
// This simulates a WestWallet IPN notification

const axios = require('axios');

const BASE_URL = process.env.BASE_URL || 'http://localhost:5173';
const IPN_URL = `${BASE_URL}/api/notifications/ipn`;

// Sample IPN data based on WestWallet documentation
// NOTE: This address needs to exist in the wallets table for the test to work
const sampleIpnData = {
  id: '123456789',
  amount: '10.50',
  address: 'TQn9Y2khEsLJW1ChVWFMSMeRDow5KcbLSE', // Sample USDT TRC-20 address
  dest_tag: '',
  label: 'team_ABC123_USDTTRC',
  currency: 'USDTTRC',
  status: 'completed',
  blockchain_hash: '72648cefcc47b4371f28dc3328bc863918913eebf81b40d4a97d577b96c1ce53',
  fee: '0.0001'
};

// Alternative test data with different scenarios
const testScenarios = [
  {
    name: 'USDT TRC-20 Payment',
    data: { ...sampleIpnData }
  },
  {
    name: 'Bitcoin Payment',
    data: {
      ...sampleIpnData,
      id: '987654321',
      currency: 'BTC',
      address: '**********************************',
      amount: '0.001',
      label: 'team_ABC123_BTC'
    }
  },
  {
    name: 'Pending Payment',
    data: {
      ...sampleIpnData,
      id: '555666777',
      status: 'pending',
      blockchain_hash: ''
    }
  }
];

async function testIpnEndpoint() {
  console.log('Testing IPN endpoint:', IPN_URL);

  try {
    // Test GET request first
    console.log('\n1. Testing GET request...');
    const getResponse = await axios.get(IPN_URL);
    console.log('GET Response:', getResponse.data);

    // Test each scenario
    for (let i = 0; i < testScenarios.length; i++) {
      const scenario = testScenarios[i];
      console.log(`\n${i + 2}. Testing ${scenario.name}...`);
      console.log('Data:', scenario.data);

      const formData = new URLSearchParams();
      Object.entries(scenario.data).forEach(([key, value]) => {
        formData.append(key, value);
      });

      try {
        const postResponse = await axios.post(IPN_URL, formData, {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'X-Forwarded-For': '***********', // Simulate WestWallet IP
          }
        });

        console.log('✅ Success:', postResponse.data);
        console.log('Status:', postResponse.status);

        // Log balance information if available
        if (postResponse.data.balance_before !== undefined) {
          console.log(`💰 Balance: $${postResponse.data.balance_before} → $${postResponse.data.balance_after}`);
          console.log(`💱 Conversion: ${postResponse.data.crypto_amount} ${postResponse.data.currency} = $${postResponse.data.usd_amount}`);
        }
      } catch (scenarioError) {
        console.error('❌ Failed:', scenarioError.response?.data || scenarioError.message);
        console.error('Status:', scenarioError.response?.status);
      }
    }

  } catch (error) {
    console.error('Error testing IPN endpoint:');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    } else {
      console.error('Message:', error.message);
    }
  }
}

// Test wallet address generation
async function testWalletGeneration() {
  console.log('\n3. Testing wallet address generation...');
  
  try {
    // This would require the server to be running and have proper API endpoints
    // For now, just log what we would test
    console.log('Would test: POST /api/wallets/generate');
    console.log('With data: { currency: "USDTTRC", teamId: "ABC123" }');
    
  } catch (error) {
    console.error('Error testing wallet generation:', error.message);
  }
}

async function testCurrencyConversion() {
  console.log('\n4. Testing currency conversion...');

  const testAmounts = [
    { amount: 10.5, currency: 'USDTTRC' },
    { amount: 0.001, currency: 'BTC' },
    { amount: 1, currency: 'ETH' },
    { amount: 100, currency: 'TRX' }
  ];

  for (const test of testAmounts) {
    console.log(`${test.amount} ${test.currency} should convert to USD`);
  }
}

async function main() {
  console.log('=== IPN Webhook Test ===');
  console.log('This test simulates WestWallet sending payment notifications');
  console.log('Make sure the server is running and has wallet addresses in the database\n');

  await testIpnEndpoint();
  await testWalletGeneration();
  await testCurrencyConversion();
  console.log('\n=== Test Complete ===');
  console.log('Check the server logs for detailed processing information');
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { testIpnEndpoint, testWalletGeneration };
