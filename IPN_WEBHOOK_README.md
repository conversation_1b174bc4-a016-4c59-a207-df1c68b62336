# WestWallet IPN Webhook Implementation

This document describes the implementation of the IPN (Instant Payment Notification) webhook for WestWallet integration.

## Overview

The IPN webhook allows WestWallet to notify our server immediately when payments are received to wallet addresses generated through their API. This enables real-time payment processing and automatic transaction recording.

## Implementation Details

### 1. Wallet Address Generation with IPN URL

**File**: `src/lib/server/walletService.js`

When generating wallet addresses, we now include an IPN URL that WestWallet will call when payments are received:

```javascript
// Generate wallet address with IPN URL
const finalIpnUrl = ipnUrl || `${baseUrl}/api/notifications/ipn`;
const addressData = await client.generateAddress(currency, finalIpnUrl, label);
```

The IPN URL is automatically constructed based on the server's base URL, or can be provided explicitly.

### 2. IPN Webhook Endpoint

**File**: `src/routes/api/notifications/ipn/+server.js`

The webhook endpoint handles incoming payment notifications from WestWallet:

- **URL**: `/api/notifications/ipn`
- **Method**: `POST`
- **Content-Type**: `application/x-www-form-urlencoded`
- **Source IP**: `***********` (WestWallet's notification server)

#### Security Features

1. **IP Validation**: Only accepts requests from WestWallet's authorized IP address
2. **Duplicate Prevention**: Uses WestWallet transaction ID as primary key to prevent duplicate processing
3. **Error Handling**: Comprehensive error handling and logging

#### Data Processing

The webhook processes the following data from WestWallet:

```javascript
{
  id: "123456789",                    // WestWallet transaction ID
  amount: "10.50",                    // Payment amount
  address: "TQn9Y2khEsLJW1ChVWFMSMeRDow5KcbLSE", // Wallet address
  currency: "USDTTRC",                // Currency code
  status: "completed",                // Transaction status
  blockchain_hash: "72648...",        // Blockchain transaction hash
  label: "team_ABC123_USDTTRC"        // Wallet label
}
```

### 3. Transaction Recording and Balance Updates

When a payment notification is received:

1. **Wallet Lookup**: Find the wallet and associated team by address
2. **Balance Calculation**: Get current team balance and convert crypto amount to USD
3. **Transaction Creation**: Create transaction record with proper balance tracking
4. **Balance Update**: Update team balance with new USD amount
5. **Notification**: Send push notification to team devices with balance information
6. **Response**: Return success/error response to WestWallet with balance details

#### Currency Conversion

The system automatically converts cryptocurrency amounts to USD using the `currencyService`:

- **USDT/USDTTRC**: 1:1 conversion (pegged to USD)
- **Other cryptocurrencies**: Uses fallback rates or real-time API rates
- **Conversion logging**: All conversions are logged for transparency

#### Database Schema

The implementation uses the existing `transactions` table and adds a stored procedure:

```sql
CREATE TABLE transactions (
    internal_id INT8 PRIMARY KEY,      -- WestWallet transaction ID
    created_at TIMESTAMPTZ NOT NULL,
    team_internal_id UUID NOT NULL,
    team_id TEXT NOT NULL,
    amount NUMERIC NOT NULL,           -- Amount in USD
    description TEXT,                  -- Includes currency, address, and conversion details
    balance_before NUMERIC NOT NULL,   -- Team balance before transaction (USD)
    balance_after NUMERIC NOT NULL     -- Team balance after transaction (USD)
);
```

#### Balance Management

- **Team Balance**: Stored in USD in the `teams.balance` field
- **Transaction Amount**: Converted to USD and stored in `transactions.amount`
- **Balance Tracking**: `balance_before` and `balance_after` track the team's USD balance
- **Sequential Updates**: Transaction record created first, then team balance updated

### 4. Push Notifications

When a payment is received, the system automatically sends push notifications to all devices in the team:

```javascript
await sendNotification({
  title: 'Payment Received',
  message: `Received ${amount} ${currency} in your wallet`,
  teamId: teamId,
  data: {
    type: 'payment_received',
    amount: amount.toString(),
    currency: currency,
    address: address,
    transaction_id: id.toString(),
    blockchain_hash: blockchain_hash || '',
    status: status
  }
});
```

## Testing

### Test Script

Use the provided test script to verify the IPN webhook:

```bash
node test_ipn.js
```

The script tests:
1. GET request to check endpoint status
2. Multiple POST scenarios with different payment types
3. Error handling for various edge cases

### Manual Testing

1. **Start the development server**:
   ```bash
   npm run dev
   ```

2. **Test the endpoint**:
   ```bash
   curl -X GET http://localhost:5173/api/notifications/ipn
   ```

3. **Simulate a payment notification**:
   ```bash
   curl -X POST http://localhost:5173/api/notifications/ipn \
     -H "Content-Type: application/x-www-form-urlencoded" \
     -H "X-Forwarded-For: ***********" \
     -d "id=123456789&amount=10.50&address=TQn9Y2khEsLJW1ChVWFMSMeRDow5KcbLSE&currency=USDTTRC&status=completed"
   ```

## Configuration

### Environment Variables

- `PUBLIC_BASE_URL` or `ORIGIN`: Base URL for constructing IPN URLs
- `NODE_ENV`: Set to 'development' to skip IP validation during testing

### WestWallet Configuration

When generating addresses, the system automatically includes the IPN URL. No additional configuration is needed on the WestWallet side.

## Error Handling

The webhook handles various error scenarios:

1. **Invalid IP**: Returns 401 Unauthorized
2. **Missing fields**: Returns 400 Bad Request
3. **Wallet not found**: Returns 404 Not Found
4. **Database errors**: Returns 500 Internal Server Error
5. **Duplicate transactions**: Returns success (idempotent)

## Monitoring

All IPN webhook activity is logged with the `[IPN]` prefix for easy monitoring:

```
[IPN] Received payment notification from WestWallet
[IPN] Request from IP: ***********
[IPN] Parsed notification data: {...}
[IPN] Found wallet for team: ABC123
[IPN] Created transaction record: 123456789
[IPN] Sent notification to team: ABC123
```

## Security Considerations

1. **IP Whitelisting**: Only WestWallet's IP (***********) is allowed
2. **Idempotency**: Duplicate notifications are handled gracefully
3. **Validation**: All incoming data is validated before processing
4. **Error Disclosure**: Minimal error information is returned to prevent information leakage

## Future Enhancements

1. **Signature Validation**: Add HMAC signature validation for additional security
2. **Retry Logic**: Implement retry mechanism for failed notifications
3. **Webhook Logs**: Store webhook events for audit purposes
4. **Balance Updates**: Automatically update team balances based on payments
