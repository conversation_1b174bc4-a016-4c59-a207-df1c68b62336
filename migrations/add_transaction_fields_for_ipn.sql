-- Migration: IPN webhook support
-- No database schema changes needed for IPN webhook functionality
-- The existing tables (teams, transactions, wallets) are sufficient

-- The IPN webhook will:
-- 1. Use existing transactions table with proper balance_before/balance_after tracking
-- 2. Update team balance in the teams table
-- 3. Store transaction amounts in USD after currency conversion

-- This is a no-op migration since the current schema supports all required functionality
