// Currency conversion service for cryptocurrency to USD conversion
import { env } from '$env/dynamic/private';

// Fallback exchange rates (should be updated with real API)
const FALLBACK_RATES = {
  'USDTTRC': 1.0,    // USDT TRC-20 is pegged to USD
  'USDT': 1.0,       // USDT is pegged to USD
  'BTC': 45000,      // Bitcoin approximate rate
  'ETH': 2500,       // Ethereum approximate rate
  'TRX': 0.08,       // Tron approximate rate
  'TON': 2.5,        // TON approximate rate
  'SOL': 100,        // Solana approximate rate
  'NOT': 0.01,       // Notcoin approximate rate
  'XMR': 150,        // Monero approximate rate
  'XRP': 0.6,        // Ripple approximate rate
  'DOGE': 0.08       // Dogecoin approximate rate
};

/**
 * Get exchange rate for a cryptocurrency to USD
 * @param {string} currency - Currency code (e.g., 'BTC', 'USDTTRC')
 * @returns {Promise<number>} Exchange rate to USD
 */
export async function getExchangeRate(currency) {
  try {
    // For stablecoins pegged to USD, return 1.0
    if (currency === 'USDTTRC' || currency === 'USDT' || currency === 'USDC') {
      return 1.0;
    }

    // Try to get real-time rates from an API (you can implement this)
    const realTimeRate = await getRealTimeRate(currency);
    if (realTimeRate) {
      return realTimeRate;
    }

    // Fall back to hardcoded rates
    const fallbackRate = FALLBACK_RATES[currency];
    if (fallbackRate) {
      console.log(`[CurrencyService] Using fallback rate for ${currency}: ${fallbackRate} USD`);
      return fallbackRate;
    }

    // If no rate found, log warning and return 0
    console.warn(`[CurrencyService] No exchange rate found for currency: ${currency}`);
    return 0;

  } catch (error) {
    console.error(`[CurrencyService] Error getting exchange rate for ${currency}:`, error);
    return FALLBACK_RATES[currency] || 0;
  }
}

/**
 * Convert cryptocurrency amount to USD
 * @param {number} amount - Amount in cryptocurrency
 * @param {string} currency - Currency code
 * @returns {Promise<number>} Amount in USD
 */
export async function convertToUSD(amount, currency) {
  try {
    const rate = await getExchangeRate(currency);
    const usdAmount = amount * rate;
    
    console.log(`[CurrencyService] Converting ${amount} ${currency} to USD: ${usdAmount} (rate: ${rate})`);
    return usdAmount;

  } catch (error) {
    console.error(`[CurrencyService] Error converting ${amount} ${currency} to USD:`, error);
    return 0;
  }
}

/**
 * Get real-time exchange rate from external API
 * @param {string} currency - Currency code
 * @returns {Promise<number|null>} Exchange rate or null if failed
 */
async function getRealTimeRate(currency) {
  try {
    // You can implement integration with CoinGecko, CoinMarketCap, or other APIs here
    // For now, return null to use fallback rates
    
    // Example implementation with CoinGecko (commented out):
    /*
    const response = await fetch(`https://api.coingecko.com/api/v3/simple/price?ids=${getCoinGeckoId(currency)}&vs_currencies=usd`);
    const data = await response.json();
    const coinId = getCoinGeckoId(currency);
    return data[coinId]?.usd || null;
    */
    
    return null;

  } catch (error) {
    console.error(`[CurrencyService] Error fetching real-time rate for ${currency}:`, error);
    return null;
  }
}

/**
 * Map currency codes to CoinGecko IDs
 * @param {string} currency - Currency code
 * @returns {string} CoinGecko ID
 */
function getCoinGeckoId(currency) {
  const mapping = {
    'BTC': 'bitcoin',
    'ETH': 'ethereum',
    'TRX': 'tron',
    'TON': 'the-open-network',
    'SOL': 'solana',
    'XMR': 'monero',
    'XRP': 'ripple',
    'DOGE': 'dogecoin',
    'USDTTRC': 'tether',
    'USDT': 'tether'
  };
  
  return mapping[currency] || currency.toLowerCase();
}

/**
 * Update exchange rates from external API (can be called periodically)
 * @returns {Promise<void>}
 */
export async function updateExchangeRates() {
  try {
    console.log('[CurrencyService] Updating exchange rates...');
    
    // Implement periodic rate updates here
    // This could be called by a cron job or scheduled task
    
    console.log('[CurrencyService] Exchange rates updated successfully');

  } catch (error) {
    console.error('[CurrencyService] Error updating exchange rates:', error);
  }
}

/**
 * Get all supported currencies and their current rates
 * @returns {Promise<Object>} Object with currency codes as keys and rates as values
 */
export async function getAllRates() {
  try {
    const rates = {};
    
    for (const currency of Object.keys(FALLBACK_RATES)) {
      rates[currency] = await getExchangeRate(currency);
    }
    
    return rates;

  } catch (error) {
    console.error('[CurrencyService] Error getting all rates:', error);
    return FALLBACK_RATES;
  }
}
